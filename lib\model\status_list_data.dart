import '../export_casekarao.dart';

class DataList {
  static List<PendingStatusItemModel> pendingList = [
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
    PendingStatusItemModel(
      title: AppStrings.kHitAndRunCase,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      time: "11:30am to 12:30pm",
      day: "Tuesday 18 Feb, 2025",
    ),
  ];

  ///OngoingList
  static List<OnGoingStatusItemModel> onGoingList = [
    OnGoingStatusItemModel(
      title: AppStrings.kMurderCase,
      title2: AppStrings.k1stMilestone,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      paymentStatus: AppStrings.kPaid,
      mileStoneExpiry: "7",
      lowyerTitle: '1st Milestone',
    ),
    OnGoingStatusItemModel(
      title: AppStrings.kMurderCase,
      title2: AppStrings.k1stMilestone,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      paymentStatus: AppStrings.kPaid,
      mileStoneExpiry: "12",
      lowyerTitle: '1st Milestone',
    ),
    OnGoingStatusItemModel(
      title: AppStrings.kMurderCase,
      title2: AppStrings.k1stMilestone,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      paymentStatus: AppStrings.kPending,
      mileStoneExpiry: "2",
      lowyerTitle: '1st Milestone',
    ),
    OnGoingStatusItemModel(
      title: AppStrings.kMurderCase,
      title2: AppStrings.k1stMilestone,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      paymentStatus: AppStrings.kPending,
      mileStoneExpiry: "2",
      lowyerTitle: '1st Milestone',
    ),
    OnGoingStatusItemModel(
      title: AppStrings.kMurderCase,
      title2: AppStrings.k1stMilestone,
      subtitle: AppStrings.kLoremIpsum,
      userImage: ImageAssets.userImage,
      userName: "Randy Press",
      paymentStatus: AppStrings.kPending,
      mileStoneExpiry: "2",
      lowyerTitle: '1st Milestone',
    ),
  ];

  ///CaseDetails data list
  static List<CaseDetailsModel> caseDetailsList = [
    CaseDetailsModel(
      mileStoneNumber: "1st Milestone",
      mileStoneStatus: "Milestone Completed",
      caseName: "Case Initialization",
      caseDesc: AppStrings.kLoremIpsumIsSimplyDummy,
    ),
    CaseDetailsModel(
      mileStoneNumber: "2nd Milestone",
      mileStoneStatus: "Milestone Completed",
      caseName: "Case Initialization",
      caseDesc: AppStrings.kLoremIpsumIsSimplyDummy,
    ),
    CaseDetailsModel(
      mileStoneNumber: "3rd Milestone",
      mileStoneStatus: "Milestone Completed",
      caseName: "Case Initialization",
      caseDesc: AppStrings.kLoremIpsumIsSimplyDummy,
    ),
    CaseDetailsModel(
      mileStoneNumber: "4th Milestone",
      mileStoneStatus: "Milestone Completed",
      caseName: "Case Initialization",
      caseDesc: AppStrings.kLoremIpsumIsSimplyDummy,
    ),
    CaseDetailsModel(
      mileStoneNumber: "5th Milestone",
      mileStoneStatus: "Milestone Pending",
      caseName: "Case Initialization",
      caseDesc: AppStrings.kLoremIpsumIsSimplyDummy,
    ),
  ];

  ///Recent Activity data List

  static List<RecentActivityModel> recentActivityList = [
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "1st Milestone",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Received",
    ),
  ];

  static List<UserMessageModel> userMessageList = [
    UserMessageModel(
      title: "Charlie Franci",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 2,
      dateTime: "Today",
    ),
    UserMessageModel(
      title: "Nolan Baptista",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 0,
      dateTime: "3:23 pm",
    ),
    UserMessageModel(
      title: "Gustavo Culhane",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 0,
      dateTime: "2:12 pm",
    ),
    UserMessageModel(
      title: "Zaire Franci",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 0,
      dateTime: "Wednesday",
    ),
    UserMessageModel(
      title: "Roger Stanton",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 0,
      dateTime: "Friday",
    ),
    UserMessageModel(
      title: "Cristofer Septimus",
      subtitle: "How are you doing?",
      userImage: ImageAssets.userImage,
      messageCount: 0,
      dateTime: "Saturday",
    ),
  ];

  static List<RecentActivityModel> payoutDetailsList = [
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "Case Completed",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Transfared",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "Case Completed",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Transfared",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "Case Completed",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Transfared",
    ),
    RecentActivityModel(
      title: "Evans Richard",
      subtitle: "Murder Case",
      milestone: "Case Completed",
      userImage: ImageAssets.userImage,
      receivedAmount: 500,
      amountStatus: "Transfared",
    ),
  ];
}
