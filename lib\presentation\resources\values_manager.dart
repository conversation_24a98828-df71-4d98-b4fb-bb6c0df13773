import 'package:flutter/material.dart';

class AppMargin {
  static const double m0 = 0.0;
  static const double m8 = 8.0;
  static const double m5 = 5.0;
  static const double m10 = 10.0;
  static const double m12 = 12.0;
  static const double m14 = 14.0;
  static const double m15 = 15.0;
  static const double m16 = 16.0;
  static const double m18 = 18.0;
  static const double m20 = 20.0;
  static const double m25 = 25.0;
}

class AppPadding {
  static const double p5 = 5.0;
  static const double p2 = 2.0;
  static const double p4 = 4.0;
  static const double p8 = 8.0;
  static const double p10 = 10.0;
  static const double p12 = 12.0;
  static const double p14 = 14.0;
  static const double p16 = 16.0;
  static const double p18 = 18.0;
  static const double p20 = 20.0;
  static const double p30 = 30.0;
  static const double p40 = 40.0;
}

class AppSize {
  static const double s1_5 = 1.5;
  static const double s4 = 4.0;
  static const double s6 = 6.0;
  static const double s8 = 8.0;
  static const double s10 = 10.0;
  static const double s12 = 12.0;
  static const double s13 = 13.0;
  static const double s14 = 14.0;
  static const double s15 = 15.0;
  static const double s16 = 16.0;
  static const double s17 = 17.0;
  static const double s18 = 18.0;
  static const double s20 = 20.0;
  static const double s22 = 22.0;
  static const double s24 = 24.0;
  static const double s26 = 24.0;
  static const double s28 = 28.0;
  static const double s30 = 30.0;
  static const double s40 = 40.0;
  static const double s50 = 50.0;
  static const double s60 = 60.0;
  static const double s70 = 70.0;
  static const double s80 = 80.0;
  static const double s65 = 65.0;
  static const double s90 = 90.0;
  static sizeWidth(BuildContext context) => MediaQuery.of(context).size.width;
  static sizeHeight(BuildContext context) => MediaQuery.of(context).size.height;
}