{
    "version": "0.2.0",
    "configurations": [
        {
            "name": "casekarao_app",
            "request": "launch",
            "type": "dart",
            // "toolArgs": [
            //     "--fvm"
            // ]
        },
        {
            "name": "casekarao_app (profile mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "profile",
            // "toolArgs": [
            //     "--fvm"
            // ]
        },
        {
            "name": "casekarao_app (release mode)",
            "request": "launch",
            "type": "dart",
            "flutterMode": "release",
            // "toolArgs": [
            //     "--fvm"
            // ]
        }
    ]
}
