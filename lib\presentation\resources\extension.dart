// ignore: camel_case_extensions

extension extString on String {
  bool get isValidEmail {
    final emailRegExp = RegExp(r"^[a-zA-Z0-9.]+@[a-zA-Z0-9]+\.[a-zA-Z]+");
    return emailRegExp.hasMatch(this);
  }

  //Capitalization
  String capitalize() {
    return "${this[0].toUpperCase()}${this.substring(1).toLowerCase()}";
  }

  bool get isValidName {
    final nameRegExp = RegExp(
      r"^\s*([A-Za-z]{1,}([\.,] |[-']| ))+[A-Za-z]+\.?\s*$",
    );
    return nameRegExp.hasMatch(this);
  }

  // bool get isValidPassword {
  //   final passwordRegExp =
  //   RegExp(r'^(?=.*?[A-Z])(?=.*?[a-z])(?=.*?[0-9])(?=.*?[!@#\$&*~]).{8,}$');
  //   return passwordRegExp.hasMatch(this);
  // }

  bool get isValidPassword {
    final passwordRegExp = RegExp(r'^(?=.*[A-Z])(?=.*\d).{8,}$');
    return passwordRegExp.hasMatch(this);
  }

  // bool get isNotNull {
  //   return this != null;
  // }

  bool get isValidPhone {
    final phoneRegExp = RegExp(r"^\+?0[0-9]{10}$");
    return phoneRegExp.hasMatch(this);
  }
}

extension StringCasingExtension on String {
  String toCapitalized() =>
      length > 0 ? '${this[0].toUpperCase()}${substring(1).toLowerCase()}' : '';
}

// extension DateFormate on String {
//   String toDateFormate() {
//     var selectdateFormate = this.split(' ')[0];
//     var a = Jiffy(selectdateFormate).yMMMMd;
//     return a;
//   }
//}
