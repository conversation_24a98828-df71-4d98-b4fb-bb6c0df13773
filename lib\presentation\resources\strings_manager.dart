class AppStrings {
  ///Figma Link
  static String FigmaFileLink =
      "https://www.figma.com/design/h2ApMIFqDvnDxCyLvSxVdF/CaseKarao-Mobile-App?node-id=6-442&t=4tojoCPuKtNAWLam-0";

  ///GetStarted
  static const String or = "Or";
  static const String login = "Login";
  static const String letsGetStarted = "Let’s Get Started";
  static const String continueWithApple = "Continue with Apple";
  static const String continueWithGoogle = "Continue with Google";
  static const String alreadyHaveAnAccount = "Already have an account?\t";
  static const String continueWithPhoneNumber = "Continue with Phone Number";
  static const String createAKaseKaraoAccountOrLogin =
      "Create a CaseKarao account or login if you already have an account";

  ///Login
  static const String password = "Password";
  static const String phoneNumber = "Phone Number";
  static const String createAccount = "Create account";
  static const String phoneNumberHintText = "Ex : +1 *********";
  static const String loginToYourAccount = "Login to your account";
  static const String dontHaveAnAccount = "Don’t have an account?\t";
  static const String createAnAccountByFillingInTheDataBelow =
      "Create an account by filling in the data below";

  ///Register
  static const String and = "and";
  static const String email = "Email";
  static const String phone = "Phone";
  static const String apple = "Apple";
  static const String google = "Google";
  static const String register = "Register";
  static const String fullName = "Full name";
  static const String privacyPolicy = "Privacy Policy";
  static const String fullNameHintText = "Ex : Baker Davis";
  static const String phoneHintText = "Ex : +971 45 462 1245";
  static const String termsAndConditions = "Terms and Conditions";
  static const String emailHintText = "Ex : <EMAIL>";
  static const String createStrongPassword = "Create strong password";
  static const String letsCreateNewAccount = "Let’s Create new account";
  static const String byProceedingYouAgreeToThe =
      "By proceeding, you agree to the\t";

  ///OTP
  static const String continues = "Continue";
  static const String resendCode = "Resend Code";
  static const String inputOTPCode = "Input OTP Code";
  static const String pleaseEnterTheOTPCodeThatWeHaveSentTo =
      "Please enter the OTP Code that we have sent to ";

  ///SetupProfile
  static const String setupProfile = "Setup Profile";
  static const String optionalDetails = "Optional Details";
  static const String legalExperience = "Legal Experience";
  static const String saveAndContinue = "Save and Continue";
  static const String barIDCardUpload = "Bar ID Card Upload";
  static const String enhancingProfile = "(Enhancing Profile)";
  static const String personalInformation = "Personal Information";
  static const String businessAndAvailability = "Business & Availability";
  static const String governmentIssuedIDUpload = "Government-issued ID Upload";
  static const String educationAndCertifications = "Education & Certifications";
  static const String selfieForIdentityVerification =
      "Selfie for Identity Verification";
  static const String noteCompleteAllRequired =
      "Note: Complete all required fields for admin approval. Incomplete profiles will be rejected and hidden from users.";
  static const String
  createYourLawyerProfileWithAccurateDetailsToHelpUsersFindYouEasily =
      "Create your lawyer profile with accurate details to help users find you easily.";

  ///Legal Experience
  static const String required = "Required";
  static const String optional = "Optional";
  static const String familyLaw = "Family Law";
  static const String ex5Years = "Ex: 5 Years";
  static const String criminalLaw = "Criminal Law";
  static const String practiceAreas = "Practice Areas";
  static const String enterTagsHere = "Enter tags here";
  static const String yearsOfExperience = "Years of Experience\t";
  static const String eCityLawSchoolAndCourt = "Ex: City Law School and Court";
  static const String exCityLawSchoolAndCourt = "Ex: City Law School and Court";
  static const String selectJurisdiction =
      "Select Jurisdiction/State of Practice";
  static const String selectJurisdiction1 =
      "Select Jurisdiction/State of Practice1";
  static const String selectJurisdiction2 =
      "Select Jurisdiction/State of Practice2";
  static const String jurisdictionStateOfPractice =
      "Jurisdiction/State of Practice\t";
  static const String jurisdictionStateOfPractice1 =
      "Jurisdiction/State of Practice1";
  static const String lawFirmOrganizationNameIfApplicable =
      "Law Firm/Organization Name (if applicable)";
  static const String barLicenseNumberWithIssuingAuthority =
      "Bar License Number (with issuing authority)\t";

  ///Education & Certifications
  static const String exLaw = "Ex : Law";
  static const String ex2012 = "Ex : 2012";
  static const String degreeEarned = "Degree Earned";
  static const String exLawSchool = "Ex : Law School";
  static const String yearOfGraduation = "Year of Graduation";
  static const String lawSchoolAttended = "Law School Attended";
  static const String exCertificateName = "Ex : Certificate name";
  static const String additionalCertificationsIfAny =
      "Additional Certifications (if any)";

  ///PersonalInformation
  static const String mM = "MM";
  static const String dD = "DD";
  static const String yYYY = "YYYY";
  static const String submit = "Submit";
  static const String sendOffer = "Send Offer";
  static const String verified = "Verified";
  static const String uploadImage = "Upload Image";
  static const String dateOfBirth = "Date of Birth";
  static const String emailAddress = "Email Address";

  ///Business & Availability
  static const String mon = "Mon";
  static const String tue = "Tue";
  static const String wed = "Wed";
  static const String thu = "Thu";
  static const String fri = "Fri";
  static const String sat = "Sat";
  static const String sun = "Sun";
  static const String to = "To";
  static const String from = "From";
  static const String hHMM = "HH:MM";
  static const String monday = "Monday";
  static const String friday = "Friday";
  static const String thursday = "Thursday";
  static const String officeAddress = "Office Address";
  static const String availabilitySchedule = "Availability Schedule";
  static const String exCompleteStreetAddress = "Ex : Complete Street Address";

  ///Optional Details
  static const String multiSelect = "Multi Select";
  static const String selectLanguage = "Select language";
  static const String languagesSpoken = "Languages Spoken\t\t";
  static const String bioProfessionalSummary = "Bio/Professional Summary";
  static const String exTellUsMoreAboutYourself =
      "Ex : Tell us more about yourself";

  ///GovernmentIssuedIDUpload
  static const String holdStill = "Hold Still";
  static const String govtIssuedCNICUpload = "Govt-issued CNIC Upload";
  static const String scanBackSideOfYourCNIC = "Scan Back side of your CNIC";
  static const String scanFrontSideOfYourCNIC = "Scan Front side of your CNIC";
  static const String
  alignYourGovernmentIssuedCNICWithinTheMarkersForAutomaticScanning =
      "Align your government-issued CNIC within the markers for automatic scanning.";

  ///Bar ID Card Upload
  static const String scanBackSideOfYourID = "Scan Back side of your ID";
  static const String scanFrontSideOfYourID = "Scan Front side of your ID";
  static const String alignYourCertificateWithinTheMarkersForAutomaticScanning =
      "Align your certificate within the markers for automatic scanning.";

  ///Selfie for Identity Verification
  static const String placeYourFaceIntoTheGridAndHoldStill =
      "Place your face into the grid and hold still";

  ///Your Application is Under Review
  static const String ifRejected = "If Rejected:";
  static const String ifApproved = "If Approved:";
  static const String whatHappensNext = "What Happens Next?";
  static const String contactSupportTeam = "Contact Support Team";
  static const String yourApplicationIsUnderReview =
      "Your Application is Under Review";
  static const String approvalTypicallyTakes2448Hours =
      "Approval typically takes 24-48 hours.";
  static const String thankYouForSubmittingYourDetails =
      "Thank you for submitting your details! ";
  static const String wellNotifyYouWithReasonsAndStepsToReapply =
      "We’ll notify you with reasons and steps to reapply";
  static const String ourAdminWillVerifyYourCredentialsAndSubmittedDocuments =
      "Our admin will verify your credentials and submitted documents.";
  static const String forAnyQueriesFeelFreeToContactOurSupportTeamStayTuned =
      "For any queries, feel free to contact our support team. Stay tuned!";
  static const String
  yourProfileWillBeVisibleToUsersAllowingThemToConnectWithYou =
      "Your profile will be visible to users, allowing them to connect with you.";
  static const String
  ourTeamIsCurrentlyReviewingYourInformationToEnsureComplianceWithOurStandards =
      "Our team is currently reviewing your information to ensure compliance with our standards.";
  static const String
  youReceiveANotificationOnceYourProfileIsApprovedOrIfAnyUpdatesAreNeeded =
      "You’ll receive a notification once your profile is approved or if any updates are needed.";

  ///Home

  static const String kPaid = "Paid";
  static const String kHome = "Home";
  static const String kDays = "Days";
  static const String kCase = "Case";
  static const String kWelcome = "Welcome";
  static const String kPending = "Pending";
  static const String kRequests = "Requests";
  static const String kViewAll = "View all";
  static const String kMessage = "Message";
  static const String kOnGoing = "On Going";
  static const String kExpired = "Expired";
  static const String kCanceled = "Canceled";
  static const String kSettings = "Settings";
  static const String kEarnings = "Earnings";
  static const String kCompleted = "Completed";
  static const String kMartkAsCompleted = "Marke as completed";
  static const String kViewPaymentReceipt = "View Payment Receipt";
  static const String kMurderCase = "Murder Case";
  static const String kViewDetails = "View details";
  static const String kAppointments = "Appointments";
  static const String kMyCases = "My Cases";
  static const String k1stMilestone = "1st Milestone";
  static const String kOnGoingCases = "On Going Cases";
  static const String kJaylonHerwitz = "Jaylon Herwitz";
  static const String kOfferRequests = "Offer Requests";
  static const String kHitAndRunCase = "Hit and Run Case";
  static const String kPaymentStatus = "Payment Status:\t";
  static const String kPaymentVerified = "Payment Verified";
  static const String kPaymentPending= "Payment Pending";
  static const String kMilestoneExpiry = "Milestone Expiry:\t";
  static const String kNewConsultationRequests = "New Consultation Requests";
  static const String kNewConsultation = "Consultations";
  static const String kLawyerDetails = "Lawyer Details";
  static const String kConsultationRequestsDetails =
      "Consultation Requests Details";
  static const String kLoremIpsumIsSimplyDummy =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.";
  static const String kLoremIpsum =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.";

  ///Consultation Requests Details

  static const String kMessageLawyer = "Message Lawyer";
  static const String kDate = "Date";
  static const String kReject = "Reject";
  static const String kAccept = "Accept";
  static const String kTimingSlot = "Timing Slot";
  static const String kCasefiledBy = "Case filed by";
  static const String kAttachedDocuments = "Attached Documents";

  ///Create Milestones
  static const String kPKR = "PKR";
  static const String kAmount = "Amount";
  static const String kDuration = "Duration";
  static const String k2ndMilestone = "2nd Milestone";
  static const String kAddaMilestone = "Add a Milestone";
  static const String kCreateMilestones = "Create Milestones";
  static const String kNameTheMilestone = "Name the Milestone";
  static const String kEnterDetailDescription = "Enter Detail Description";
  static const String kUseMilestoneIsDeliver =
      "Use Milestone is deliver step by step for more complex cases.";
  static const String kPaymentForDeliveredMilestones =
      "Payment for delivered milestones will be available after the entire case is marked as completed or in case the client decides not to continue to the next milestone.";

  ///Case Details
  static const String kPDF = "PDF";
  static const String k2pages = "2 pages";
  static const String kDocuments = "Documents";
  static const String kAddDispute = "Add Dispute";
  static const String kSendOfferToLawyer = "Send Offer to Lawyer";
  static const String kUploadDocument = "Upload Document";
  static const String kCaseDiscussion = "Case Discussion";
  static const String kUploadedByLawyer = "Uploaded by Lawyer";
  static const String kClosingTheCase = "Closing the Case";
  static const String kCaseCompletionConfirmation =
      "Are your sure your case has been completed from the lawyer side?";
  static const String kYes = "Yes";
  static const String kNo = "No";

  ///Case Discussion
  static const String kTypeAMessage = "Type a message ...";
  static const String kTotalEarning = "TotalEarning";
  static const String kReceived = "Received";
  static const String kViewOffer = "View Offer";
  static const String kRecentActivity = "Recent Activity";

  ///Settings
  static const String kLogout = "Logout";
  static const String kViewProfile = "View Profile";
  static const String kAccount = "Account";
  static const String kAccountInformation = "Account Information";
  static const String kChangePassword = "Change Password";
  static const String kDispute = "Dispute";
  static const String kNotifications = "Notifications";
  static const String kPayout = "Payout";
  static const String kPayoutDetails = "Payout Details";
  static const String kHelpSupport = "Help & Support";
  static const String kLiveChat = "Live Chat";
  static const String kTermConditions = "Term & Conditions";
  static const String kPrivacyPolicy = "Privacy Policy";
  static const String kAboutCaseKarao = "About CaseKarao";

  ///Payout Details
  static const String kAddPayout = "Add Payout";
  static const String kTransfered = "Transfered";
  static const String kRecentPayoutDetails = "Recent Payout Details";

  ///Add Payout
  static const String kAccountTitleHintText = "e.g: Nathan Drake";
  static const String kAccountTitle = "Account Title";
  static const String kAccountNumber = "Account Number";
  static const String kRoutingNumber = "Routing Number";
  static const String kFetchedDetails = "Fetched Details";
  static const String kMakeItDefaultPayoutDetails =
      "Make it default payout details";

  ///Notification Settings
  static const String kNotificationSettings = "Notification Settings";
  static const String kEnablePushNotification = "Enable Push Notification";
  static const String kMilestoneNotification = "Milestone Notification";
  static const String kPayoutNotification = "Payout Notification";
  static const String kNewCaseNotification = "New Case Notification";

  ///Dispute
  static const String kDisputeLoremIpsum =
      "Lorem Ipsum is simply dummy text of";
  static const String kRejected = "Rejected";
  static const String kApproved = "Approved";

  ///Dispute
  static const String kAttachMedia = "Attach Media";
  static const String kAttachDocument = "Attach Document";
  static const String kDisputeTitle = "Dispute Title";
  static const String kCaseDetaildescription =
      "Ex: Enter detail description about the case";
  static const String kCaseTitle = "Case Title";
  static const String kCaseAmount = "Amount";
  static const String kDisputeDescription = "Dispute Description";
  static const String kCaseDescription = "Case Description";
  static const String kDisputeTitleHinttext = "e.g: Case name or Case ID";
  static const String kCorporateIssue = "Ex : Corporate Issue";
  static const String kPleaseUploadAttachmentsMax5 =
      "Please upload attachments (max 5) ";

  ///Live chat
  static const String kStartLiveChat = "Start Live Chat";
  static const String kFiveMinReplyTime = "~5 mins reply time";
  static const String kChatWithOurLiveRepresentative =
      "Chat with our live representative.";
  static const String kAllConversationsAreSafePrivate =
      "All conversations are safe & private.";

  ///termsCondition
  static const String kTermsAndConditions = "Terms & Conditions";

  // static const String kAboutCaseKarao= "About Case Karao";

  ///ChangePassword
  static const String kTOldPassword = "Old Password";
  static const String kNewPassword = "New Password";
  static const String kPassword = "Password";
  static const String kConfirmPassword = "Confirm password";
  static const String kCreateStrongPassword = "Create strong password";
  static const String kInOrderToChange =
      "In order to change password please enter your old password.";
  static const String kPleaseSetYourNewPassword =
      "Please set your new password to change it.";

  /// Account Information
  static const String kLegalExperience = "Legal Experience";
  static const String kAccountPreferences = "Account Preferences";
  static const String kPersonalInformation = "Personal Information";
  static const String kBusinessAvailability = "Business & Availabilitye";
  static const String kEducationCertifications = "Education & Certifications";
  static const String kGovernmentIissuedIDUpload =
      "Government-issued ID Upload";
  static const String kBarIDCardUpload = "Bar ID Card Upload";
  static const String kOptionalDetails = "Optional Details";
  static const String kEnhancingProfile = "(Enhancing Profile)";
  static const String kSelfieforIdentityVerification =
      "Selfie for Identity Verification";

  ///Review And Rating
  static const String kHowWasYourExperience = "How was your Experience?";
  static const String kShareYourThoughts = "Share your thoughts on your experience with lawyer...";
  static const String kShareYourValuableComment = "Share your valuable comment";
  static const String kThankYouForYourReview = "Thank you for your review!";
  static const String kAttachRelevantDocuments =
      "Attach any relevant documents (optional)";
  static const String kRateYourExperience = "Rate your experience";
  static const String kRatingDescription =
      "Lorem Ipsum is simply dummy text of the printing and typesetting industry.";

  ///ConsultationRequestsDetailsUserScreen
  static const String kDelete = "Delete";
  static const String kEdit = "Edit";



  ///Validator
  static const String otpCode = "Otp Code";
  static const String otpValidator = "Please fill all Fields";
  static const String enterEmailAddress = "Enter email address";
  static const String enterFullName = "Enter full name";
  static const String enterPhoneNumber = "Enter phone number";
  static const String enterConfirmPassword = "Enter confirm Password ";
  static const String enterPassword = "Enter password";
  static const String passwordNotMatched = "Password not matched";
  static const String pleaseSelectTermCondition =
      "Please Select Terms and Condition, Privacy Policy";
  static const passwordValidator =
      "Contain at least 1 uppercase letter, 8 characters, 1 number";

  //ratee and reviews
  static const String kRateReview = "Rate & Review";
}
